-- =====================================================
-- TINDAHAN STORE - CONSOLIDATED DATABASE SCHEMA
-- =====================================================
-- Professional database setup for Tindahan store management system
-- This file contains all necessary tables, views, functions, triggers, and sample data
-- Can be directly pasted into Supabase SQL Editor for complete setup
--
-- Features:
-- ✅ Product Management with inventory tracking
-- ✅ Customer Profile Management with Cloudinary support
-- ✅ Debt Management with automatic balance calculation
-- ✅ Payment Processing with real-time balance updates
-- ✅ Comprehensive indexing for optimal performance
-- ✅ Automatic timestamp management
-- ✅ Sample data for immediate testing
-- =====================================================

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- =====================================================
-- PRODUCTS TABLE
-- =====================================================
-- Manages store inventory with stock tracking
CREATE TABLE IF NOT EXISTS products (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    image_url TEXT,
    net_weight VARCHAR(100) NOT NULL,
    price DECIMAL(10,2) NOT NULL CHECK (price >= 0),
    stock_quantity INTEGER NOT NULL DEFAULT 0 CHECK (stock_quantity >= 0),
    category VARCHAR(100) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- CUSTOMERS TABLE
-- =====================================================
-- Manages customer profiles with Cloudinary image support
CREATE TABLE IF NOT EXISTS customers (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    customer_name VARCHAR(255) NOT NULL,
    customer_family_name VARCHAR(255) NOT NULL,
    profile_picture_url TEXT,
    profile_picture_public_id TEXT, -- Cloudinary public ID for image management
    phone_number VARCHAR(20),
    address TEXT,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(customer_name, customer_family_name)
);

-- =====================================================
-- CUSTOMER DEBTS TABLE
-- =====================================================
-- Tracks customer debt records with automatic total calculation
CREATE TABLE IF NOT EXISTS customer_debts (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    customer_name VARCHAR(255) NOT NULL,
    customer_family_name VARCHAR(255) NOT NULL,
    product_name VARCHAR(255) NOT NULL,
    product_price DECIMAL(10,2) NOT NULL CHECK (product_price >= 0),
    quantity INTEGER NOT NULL CHECK (quantity > 0),
    total_amount DECIMAL(10,2) GENERATED ALWAYS AS (product_price * quantity) STORED,
    debt_date DATE NOT NULL DEFAULT CURRENT_DATE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- CUSTOMER PAYMENTS TABLE
-- =====================================================
-- Tracks customer payments with multiple payment methods
CREATE TABLE IF NOT EXISTS customer_payments (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    customer_name VARCHAR(255) NOT NULL,
    customer_family_name VARCHAR(255) NOT NULL,
    payment_amount DECIMAL(10,2) NOT NULL CHECK (payment_amount > 0),
    payment_date DATE NOT NULL DEFAULT CURRENT_DATE,
    payment_method VARCHAR(50) DEFAULT 'Cash',
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- CUSTOMER BALANCES VIEW
-- =====================================================
-- Real-time balance calculation view that automatically updates
-- when payments are made or debts are added
CREATE OR REPLACE VIEW customer_balances AS
SELECT
    customer_name,
    customer_family_name,
    COALESCE(total_debt, 0) as total_debt,
    COALESCE(total_payments, 0) as total_payments,
    COALESCE(total_debt, 0) - COALESCE(total_payments, 0) as remaining_balance,
    last_debt_date,
    last_payment_date,
    -- Additional calculated fields for better debt management
    CASE
        WHEN COALESCE(total_debt, 0) - COALESCE(total_payments, 0) <= 0 THEN 'PAID'
        WHEN COALESCE(total_debt, 0) - COALESCE(total_payments, 0) > 0 THEN 'OUTSTANDING'
        ELSE 'NO_DEBT'
    END as debt_status,
    -- Days since last payment (useful for follow-up)
    CASE
        WHEN last_payment_date IS NOT NULL THEN CURRENT_DATE - last_payment_date
        ELSE NULL
    END as days_since_last_payment,
    -- Days since last debt (useful for aging analysis)
    CASE
        WHEN last_debt_date IS NOT NULL THEN CURRENT_DATE - last_debt_date
        ELSE NULL
    END as days_since_last_debt
FROM (
    SELECT
        customer_name,
        customer_family_name,
        SUM(total_amount) as total_debt,
        MAX(debt_date) as last_debt_date
    FROM customer_debts
    GROUP BY customer_name, customer_family_name
) debts
FULL OUTER JOIN (
    SELECT
        customer_name,
        customer_family_name,
        SUM(payment_amount) as total_payments,
        MAX(payment_date) as last_payment_date
    FROM customer_payments
    GROUP BY customer_name, customer_family_name
) payments USING (customer_name, customer_family_name);

-- =====================================================
-- PERFORMANCE INDEXES
-- =====================================================
-- Optimized indexes for fast queries and better performance

-- Product indexes
CREATE INDEX IF NOT EXISTS idx_products_category ON products(category);
CREATE INDEX IF NOT EXISTS idx_products_name ON products(name);
CREATE INDEX IF NOT EXISTS idx_products_price ON products(price);
CREATE INDEX IF NOT EXISTS idx_products_stock ON products(stock_quantity);

-- Customer indexes
CREATE INDEX IF NOT EXISTS idx_customers_name ON customers(customer_name, customer_family_name);
CREATE INDEX IF NOT EXISTS idx_customers_phone ON customers(phone_number);

-- Customer debts indexes
CREATE INDEX IF NOT EXISTS idx_customer_debts_customer ON customer_debts(customer_name, customer_family_name);
CREATE INDEX IF NOT EXISTS idx_customer_debts_date ON customer_debts(debt_date);
CREATE INDEX IF NOT EXISTS idx_customer_debts_product ON customer_debts(product_name);
CREATE INDEX IF NOT EXISTS idx_customer_debts_amount ON customer_debts(total_amount);

-- Customer payments indexes
CREATE INDEX IF NOT EXISTS idx_customer_payments_customer ON customer_payments(customer_name, customer_family_name);
CREATE INDEX IF NOT EXISTS idx_customer_payments_date ON customer_payments(payment_date);
CREATE INDEX IF NOT EXISTS idx_customer_payments_method ON customer_payments(payment_method);
CREATE INDEX IF NOT EXISTS idx_customer_payments_amount ON customer_payments(payment_amount);

-- =====================================================
-- FUNCTIONS AND TRIGGERS
-- =====================================================
-- Automatic timestamp management and business logic

-- Function to automatically update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Function to validate payment amount against remaining balance
CREATE OR REPLACE FUNCTION validate_payment_amount()
RETURNS TRIGGER AS $$
DECLARE
    current_balance DECIMAL(10,2);
BEGIN
    -- Get current remaining balance for the customer
    SELECT COALESCE(remaining_balance, 0) INTO current_balance
    FROM customer_balances
    WHERE customer_name = NEW.customer_name
    AND customer_family_name = NEW.customer_family_name;

    -- Allow payment even if it exceeds balance (overpayment scenario)
    -- But log a notice for potential overpayment
    IF NEW.payment_amount > current_balance AND current_balance > 0 THEN
        RAISE NOTICE 'Payment amount (%) exceeds remaining balance (%) for customer % %',
            NEW.payment_amount, current_balance, NEW.customer_name, NEW.customer_family_name;
    END IF;

    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers to automatically update updated_at timestamps
DROP TRIGGER IF EXISTS update_products_updated_at ON products;
CREATE TRIGGER update_products_updated_at
    BEFORE UPDATE ON products
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_customers_updated_at ON customers;
CREATE TRIGGER update_customers_updated_at
    BEFORE UPDATE ON customers
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_customer_debts_updated_at ON customer_debts;
CREATE TRIGGER update_customer_debts_updated_at
    BEFORE UPDATE ON customer_debts
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_customer_payments_updated_at ON customer_payments;
CREATE TRIGGER update_customer_payments_updated_at
    BEFORE UPDATE ON customer_payments
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Create trigger to validate payment amounts
DROP TRIGGER IF EXISTS validate_payment_trigger ON customer_payments;
CREATE TRIGGER validate_payment_trigger
    BEFORE INSERT ON customer_payments
    FOR EACH ROW
    EXECUTE FUNCTION validate_payment_amount();

-- =====================================================
-- SAMPLE DATA FOR TESTING
-- =====================================================
-- Comprehensive sample data to test all functionality immediately

-- Insert sample products with diverse categories (with safe duplicate handling)
INSERT INTO products (name, net_weight, price, stock_quantity, category)
SELECT 'Lucky Me Pancit Canton', '60g', 15.00, 50, 'Instant Foods'
WHERE NOT EXISTS (SELECT 1 FROM products WHERE name = 'Lucky Me Pancit Canton');

INSERT INTO products (name, net_weight, price, stock_quantity, category)
SELECT 'Coca-Cola', '330ml', 25.00, 30, 'Beverages'
WHERE NOT EXISTS (SELECT 1 FROM products WHERE name = 'Coca-Cola');

INSERT INTO products (name, net_weight, price, stock_quantity, category)
SELECT 'Corned Beef', '150g', 45.00, 20, 'Canned Goods'
WHERE NOT EXISTS (SELECT 1 FROM products WHERE name = 'Corned Beef');

INSERT INTO products (name, net_weight, price, stock_quantity, category)
SELECT 'Shampoo Sachet', '12ml', 8.00, 100, 'Personal Care'
WHERE NOT EXISTS (SELECT 1 FROM products WHERE name = 'Shampoo Sachet');

INSERT INTO products (name, net_weight, price, stock_quantity, category)
SELECT 'Instant Coffee', '25g', 12.00, 75, 'Beverages'
WHERE NOT EXISTS (SELECT 1 FROM products WHERE name = 'Instant Coffee');

INSERT INTO products (name, net_weight, price, stock_quantity, category)
SELECT 'Rice', '1kg', 55.00, 25, 'Rice & Grains'
WHERE NOT EXISTS (SELECT 1 FROM products WHERE name = 'Rice');

INSERT INTO products (name, net_weight, price, stock_quantity, category)
SELECT 'Soy Sauce', '200ml', 18.00, 40, 'Condiments'
WHERE NOT EXISTS (SELECT 1 FROM products WHERE name = 'Soy Sauce');

INSERT INTO products (name, net_weight, price, stock_quantity, category)
SELECT 'Detergent Powder', '35g', 6.00, 80, 'Household Items'
WHERE NOT EXISTS (SELECT 1 FROM products WHERE name = 'Detergent Powder');

INSERT INTO products (name, net_weight, price, stock_quantity, category)
SELECT 'Bread Loaf', '450g', 35.00, 15, 'Bakery'
WHERE NOT EXISTS (SELECT 1 FROM products WHERE name = 'Bread Loaf');

INSERT INTO products (name, net_weight, price, stock_quantity, category)
SELECT 'Cooking Oil', '1L', 85.00, 12, 'Cooking Essentials'
WHERE NOT EXISTS (SELECT 1 FROM products WHERE name = 'Cooking Oil');

-- Insert sample customer profiles (with safe duplicate handling)
INSERT INTO customers (customer_name, customer_family_name, phone_number, address, notes)
SELECT 'Juan', 'Dela Cruz', '09123456789', 'Barangay San Jose, Quezon City', 'Regular customer - prefers instant foods'
WHERE NOT EXISTS (SELECT 1 FROM customers WHERE customer_name = 'Juan' AND customer_family_name = 'Dela Cruz');

INSERT INTO customers (customer_name, customer_family_name, phone_number, address, notes)
SELECT 'Maria', 'Santos', '09234567890', 'Barangay Maligaya, Manila', 'Frequent buyer of beverages'
WHERE NOT EXISTS (SELECT 1 FROM customers WHERE customer_name = 'Maria' AND customer_family_name = 'Santos');

INSERT INTO customers (customer_name, customer_family_name, phone_number, address, notes)
SELECT 'Pedro', 'Garcia', '09345678901', 'Barangay Bagong Silang, Caloocan', 'Prefers canned goods'
WHERE NOT EXISTS (SELECT 1 FROM customers WHERE customer_name = 'Pedro' AND customer_family_name = 'Garcia');

INSERT INTO customers (customer_name, customer_family_name, phone_number, address, notes)
SELECT 'Ana', 'Reyes', '09456789012', 'Barangay Tatalon, Quezon City', 'Coffee lover - regular customer'
WHERE NOT EXISTS (SELECT 1 FROM customers WHERE customer_name = 'Ana' AND customer_family_name = 'Reyes');

INSERT INTO customers (customer_name, customer_family_name, phone_number, address, notes)
SELECT 'Jose', 'Cruz', '09567890123', 'Barangay Payatas, Quezon City', 'Bulk rice buyer'
WHERE NOT EXISTS (SELECT 1 FROM customers WHERE customer_name = 'Jose' AND customer_family_name = 'Cruz');

INSERT INTO customers (customer_name, customer_family_name, phone_number, address, notes)
SELECT 'Rosa', 'Mendoza', '***********', 'Barangay Commonwealth, Quezon City', 'Family with young children'
WHERE NOT EXISTS (SELECT 1 FROM customers WHERE customer_name = 'Rosa' AND customer_family_name = 'Mendoza');

INSERT INTO customers (customer_name, customer_family_name, phone_number, address, notes)
SELECT 'Carlos', 'Villanueva', '***********', 'Barangay Fairview, Quezon City', 'Small business owner'
WHERE NOT EXISTS (SELECT 1 FROM customers WHERE customer_name = 'Carlos' AND customer_family_name = 'Villanueva');

-- Insert sample customer debt records (with safe duplicate handling)
INSERT INTO customer_debts (customer_name, customer_family_name, product_name, product_price, quantity, debt_date)
SELECT 'Juan', 'Dela Cruz', 'Lucky Me Pancit Canton', 15.00, 2, '2024-01-15'
WHERE NOT EXISTS (
    SELECT 1 FROM customer_debts
    WHERE customer_name = 'Juan' AND customer_family_name = 'Dela Cruz'
    AND product_name = 'Lucky Me Pancit Canton' AND debt_date = '2024-01-15'
);

INSERT INTO customer_debts (customer_name, customer_family_name, product_name, product_price, quantity, debt_date)
SELECT 'Juan', 'Dela Cruz', 'Instant Coffee', 12.00, 1, '2024-01-20'
WHERE NOT EXISTS (
    SELECT 1 FROM customer_debts
    WHERE customer_name = 'Juan' AND customer_family_name = 'Dela Cruz'
    AND product_name = 'Instant Coffee' AND debt_date = '2024-01-20'
);

INSERT INTO customer_debts (customer_name, customer_family_name, product_name, product_price, quantity, debt_date)
SELECT 'Maria', 'Santos', 'Coca-Cola', 25.00, 1, '2024-01-16'
WHERE NOT EXISTS (
    SELECT 1 FROM customer_debts
    WHERE customer_name = 'Maria' AND customer_family_name = 'Santos'
    AND product_name = 'Coca-Cola' AND debt_date = '2024-01-16'
);

INSERT INTO customer_debts (customer_name, customer_family_name, product_name, product_price, quantity, debt_date)
SELECT 'Maria', 'Santos', 'Bread Loaf', 35.00, 1, '2024-01-22'
WHERE NOT EXISTS (
    SELECT 1 FROM customer_debts
    WHERE customer_name = 'Maria' AND customer_family_name = 'Santos'
    AND product_name = 'Bread Loaf' AND debt_date = '2024-01-22'
);

INSERT INTO customer_debts (customer_name, customer_family_name, product_name, product_price, quantity, debt_date)
SELECT 'Pedro', 'Garcia', 'Corned Beef', 45.00, 1, '2024-01-17'
WHERE NOT EXISTS (
    SELECT 1 FROM customer_debts
    WHERE customer_name = 'Pedro' AND customer_family_name = 'Garcia'
    AND product_name = 'Corned Beef' AND debt_date = '2024-01-17'
);

INSERT INTO customer_debts (customer_name, customer_family_name, product_name, product_price, quantity, debt_date)
SELECT 'Pedro', 'Garcia', 'Rice', 55.00, 1, '2024-01-25'
WHERE NOT EXISTS (
    SELECT 1 FROM customer_debts
    WHERE customer_name = 'Pedro' AND customer_family_name = 'Garcia'
    AND product_name = 'Rice' AND debt_date = '2024-01-25'
);

INSERT INTO customer_debts (customer_name, customer_family_name, product_name, product_price, quantity, debt_date)
SELECT 'Ana', 'Reyes', 'Instant Coffee', 12.00, 3, '2024-01-18'
WHERE NOT EXISTS (
    SELECT 1 FROM customer_debts
    WHERE customer_name = 'Ana' AND customer_family_name = 'Reyes'
    AND product_name = 'Instant Coffee' AND debt_date = '2024-01-18'
);

INSERT INTO customer_debts (customer_name, customer_family_name, product_name, product_price, quantity, debt_date)
SELECT 'Jose', 'Cruz', 'Rice', 55.00, 1, '2024-01-19'
WHERE NOT EXISTS (
    SELECT 1 FROM customer_debts
    WHERE customer_name = 'Jose' AND customer_family_name = 'Cruz'
    AND product_name = 'Rice' AND debt_date = '2024-01-19'
);

INSERT INTO customer_debts (customer_name, customer_family_name, product_name, product_price, quantity, debt_date)
SELECT 'Jose', 'Cruz', 'Cooking Oil', 85.00, 1, '2024-01-28'
WHERE NOT EXISTS (
    SELECT 1 FROM customer_debts
    WHERE customer_name = 'Jose' AND customer_family_name = 'Cruz'
    AND product_name = 'Cooking Oil' AND debt_date = '2024-01-28'
);

INSERT INTO customer_debts (customer_name, customer_family_name, product_name, product_price, quantity, debt_date)
SELECT 'Rosa', 'Mendoza', 'Shampoo Sachet', 8.00, 5, '2024-01-21'
WHERE NOT EXISTS (
    SELECT 1 FROM customer_debts
    WHERE customer_name = 'Rosa' AND customer_family_name = 'Mendoza'
    AND product_name = 'Shampoo Sachet' AND debt_date = '2024-01-21'
);

INSERT INTO customer_debts (customer_name, customer_family_name, product_name, product_price, quantity, debt_date)
SELECT 'Carlos', 'Villanueva', 'Detergent Powder', 6.00, 10, '2024-01-23'
WHERE NOT EXISTS (
    SELECT 1 FROM customer_debts
    WHERE customer_name = 'Carlos' AND customer_family_name = 'Villanueva'
    AND product_name = 'Detergent Powder' AND debt_date = '2024-01-23'
);
-- Insert sample payment records to demonstrate balance calculation (with safe duplicate handling)
INSERT INTO customer_payments (customer_name, customer_family_name, payment_amount, payment_date, payment_method, notes)
SELECT 'Juan', 'Dela Cruz', 15.00, '2024-01-20', 'Cash', 'Partial payment for pancit canton'
WHERE NOT EXISTS (
    SELECT 1 FROM customer_payments
    WHERE customer_name = 'Juan' AND customer_family_name = 'Dela Cruz'
    AND payment_date = '2024-01-20' AND payment_amount = 15.00
);

INSERT INTO customer_payments (customer_name, customer_family_name, payment_amount, payment_date, payment_method, notes)
SELECT 'Maria', 'Santos', 25.00, '2024-01-21', 'GCash', 'Full payment for Coca-Cola'
WHERE NOT EXISTS (
    SELECT 1 FROM customer_payments
    WHERE customer_name = 'Maria' AND customer_family_name = 'Santos'
    AND payment_date = '2024-01-21' AND payment_amount = 25.00
);

INSERT INTO customer_payments (customer_name, customer_family_name, payment_amount, payment_date, payment_method, notes)
SELECT 'Pedro', 'Garcia', 50.00, '2024-01-26', 'Cash', 'Partial payment for recent purchases'
WHERE NOT EXISTS (
    SELECT 1 FROM customer_payments
    WHERE customer_name = 'Pedro' AND customer_family_name = 'Garcia'
    AND payment_date = '2024-01-26' AND payment_amount = 50.00
);

INSERT INTO customer_payments (customer_name, customer_family_name, payment_amount, payment_date, payment_method, notes)
SELECT 'Ana', 'Reyes', 36.00, '2024-01-24', 'PayMaya', 'Full payment for coffee'
WHERE NOT EXISTS (
    SELECT 1 FROM customer_payments
    WHERE customer_name = 'Ana' AND customer_family_name = 'Reyes'
    AND payment_date = '2024-01-24' AND payment_amount = 36.00
);

INSERT INTO customer_payments (customer_name, customer_family_name, payment_amount, payment_date, payment_method, notes)
SELECT 'Rosa', 'Mendoza', 20.00, '2024-01-27', 'Cash', 'Partial payment for shampoo'
WHERE NOT EXISTS (
    SELECT 1 FROM customer_payments
    WHERE customer_name = 'Rosa' AND customer_family_name = 'Mendoza'
    AND payment_date = '2024-01-27' AND payment_amount = 20.00
);

-- =====================================================
-- VERIFICATION AND SETUP CONFIRMATION
-- =====================================================
-- Queries to verify successful setup

-- Verify tables were created
SELECT 'Database setup completed successfully!' as status;

SELECT 'Tables created:' as info;
SELECT table_name,
       (SELECT COUNT(*) FROM information_schema.columns WHERE table_name = t.table_name AND table_schema = 'public') as column_count
FROM information_schema.tables t
WHERE table_schema = 'public'
AND table_name IN ('products', 'customers', 'customer_debts', 'customer_payments')
ORDER BY table_name;

-- Verify views were created
SELECT 'Views created:' as info;
SELECT table_name as view_name
FROM information_schema.views
WHERE table_schema = 'public'
AND table_name = 'customer_balances';

-- Verify indexes were created
SELECT 'Indexes created:' as info;
SELECT COUNT(*) as index_count
FROM pg_indexes
WHERE schemaname = 'public'
AND indexname LIKE 'idx_%';

-- Sample data verification
SELECT 'Sample data summary:' as info;
SELECT 'Products: ' || COUNT(*) as count FROM products
UNION ALL
SELECT 'Customers: ' || COUNT(*) as count FROM customers
UNION ALL
SELECT 'Customer Debts: ' || COUNT(*) as count FROM customer_debts
UNION ALL
SELECT 'Customer Payments: ' || COUNT(*) as count FROM customer_payments
UNION ALL
SELECT 'Customer Balances: ' || COUNT(*) as count FROM customer_balances;

-- Show customer balance summary for immediate verification
SELECT 'Customer Balance Summary:' as info;
SELECT
    customer_name || ' ' || customer_family_name as customer,
    '₱' || total_debt::text as total_debt,
    '₱' || total_payments::text as total_paid,
    '₱' || remaining_balance::text as balance,
    debt_status
FROM customer_balances
ORDER BY remaining_balance DESC;

-- =====================================================
-- SETUP COMPLETE
-- =====================================================
-- Your Tindahan store database is now ready!
--
-- Key Features Available:
-- ✅ Product inventory management
-- ✅ Customer profile management with Cloudinary support
-- ✅ Debt tracking with automatic balance calculation
-- ✅ Payment processing with real-time balance updates
-- ✅ Comprehensive reporting through customer_balances view
-- ✅ Performance optimized with proper indexing
-- ✅ Automatic timestamp management
-- ✅ Data validation and business logic enforcement
--
-- Next Steps:
-- 1. Test the payment functionality by adding payments for customers
-- 2. Verify that balances update automatically in the customer_balances view
-- 3. Use the API endpoints to interact with the database
-- 4. Monitor performance and add additional indexes if needed
-- =====================================================
