#!/usr/bin/env node

/**
 * Test Script for Debt Management System
 * 
 * This script tests the debt management functionality including:
 * - Database schema validation
 * - Payment processing
 * - Balance calculation
 * - API endpoints
 * 
 * Run with: node scripts/test-debt-system.js
 */

const fs = require('fs')
const path = require('path')

console.log('🧪 Testing Tindahan Debt Management System')
console.log('==========================================\n')

// Test 1: Validate Database Schema
function testDatabaseSchema() {
  console.log('📋 Test 1: Database Schema Validation')
  
  const schemaPath = path.join(process.cwd(), 'database', 'schema.sql')
  
  if (!fs.existsSync(schemaPath)) {
    console.log('❌ Database schema file not found')
    return false
  }
  
  const schemaContent = fs.readFileSync(schemaPath, 'utf8')
  
  // Check for required tables
  const requiredTables = [
    'products',
    'customers', 
    'customer_debts',
    'customer_payments'
  ]
  
  const requiredViews = [
    'customer_balances'
  ]
  
  const requiredFunctions = [
    'update_updated_at_column',
    'validate_payment_amount'
  ]
  
  let allTestsPassed = true
  
  // Test tables
  requiredTables.forEach(table => {
    if (schemaContent.includes(`CREATE TABLE IF NOT EXISTS ${table}`)) {
      console.log(`✅ Table '${table}' found`)
    } else {
      console.log(`❌ Table '${table}' missing`)
      allTestsPassed = false
    }
  })
  
  // Test views
  requiredViews.forEach(view => {
    if (schemaContent.includes(`CREATE OR REPLACE VIEW ${view}`)) {
      console.log(`✅ View '${view}' found`)
    } else {
      console.log(`❌ View '${view}' missing`)
      allTestsPassed = false
    }
  })
  
  // Test functions
  requiredFunctions.forEach(func => {
    if (schemaContent.includes(`CREATE OR REPLACE FUNCTION ${func}`)) {
      console.log(`✅ Function '${func}' found`)
    } else {
      console.log(`❌ Function '${func}' missing`)
      allTestsPassed = false
    }
  })
  
  // Test indexes
  const indexCount = (schemaContent.match(/CREATE INDEX IF NOT EXISTS/g) || []).length
  console.log(`✅ Found ${indexCount} performance indexes`)
  
  // Test triggers
  const triggerCount = (schemaContent.match(/CREATE TRIGGER/g) || []).length
  console.log(`✅ Found ${triggerCount} database triggers`)
  
  // Test sample data
  if (schemaContent.includes('INSERT INTO products') && 
      schemaContent.includes('INSERT INTO customer_debts') &&
      schemaContent.includes('INSERT INTO customer_payments')) {
    console.log('✅ Sample data included')
  } else {
    console.log('❌ Sample data missing')
    allTestsPassed = false
  }
  
  console.log(allTestsPassed ? '✅ Database schema validation passed\n' : '❌ Database schema validation failed\n')
  return allTestsPassed
}

// Test 2: Validate API Structure
function testAPIStructure() {
  console.log('🔌 Test 2: API Structure Validation')
  
  const apiRoutes = [
    'src/app/api/debts/route.ts',
    'src/app/api/debts/[id]/route.ts',
    'src/app/api/payments/route.ts',
    'src/app/api/payments/[id]/route.ts',
    'src/app/api/customer-balances/route.ts'
  ]
  
  let allTestsPassed = true
  
  apiRoutes.forEach(route => {
    const routePath = path.join(process.cwd(), route)
    if (fs.existsSync(routePath)) {
      console.log(`✅ API route '${route}' found`)
      
      // Check for enhanced payment processing
      if (route.includes('payments/route.ts')) {
        const content = fs.readFileSync(routePath, 'utf8')
        if (content.includes('balance_info') && content.includes('customer_balances')) {
          console.log('✅ Enhanced payment processing found')
        } else {
          console.log('❌ Enhanced payment processing missing')
          allTestsPassed = false
        }
      }
    } else {
      console.log(`❌ API route '${route}' missing`)
      allTestsPassed = false
    }
  })
  
  console.log(allTestsPassed ? '✅ API structure validation passed\n' : '❌ API structure validation failed\n')
  return allTestsPassed
}

// Test 3: Validate Component Structure
function testComponentStructure() {
  console.log('🧩 Test 3: Component Structure Validation')
  
  const requiredComponents = [
    'src/components/DebtsSection.tsx',
    'src/components/PaymentModal.tsx',
    'src/components/DebtModal.tsx',
    'src/components/DashboardStats.tsx',
    'src/components/Toast.tsx'
  ]
  
  let allTestsPassed = true
  
  requiredComponents.forEach(component => {
    const componentPath = path.join(process.cwd(), component)
    if (fs.existsSync(componentPath)) {
      console.log(`✅ Component '${component}' found`)
      
      // Check for enhanced features
      const content = fs.readFileSync(componentPath, 'utf8')
      
      if (component.includes('PaymentModal.tsx')) {
        if (content.includes('successMessage') && content.includes('errorMessage')) {
          console.log('✅ Enhanced payment feedback found')
        } else {
          console.log('❌ Enhanced payment feedback missing')
          allTestsPassed = false
        }
      }
      
      if (component.includes('DebtsSection.tsx')) {
        if (content.includes('fetchCustomerBalances') && content.includes('RefreshCw')) {
          console.log('✅ Real-time balance updates found')
        } else {
          console.log('❌ Real-time balance updates missing')
          allTestsPassed = false
        }
      }
    } else {
      console.log(`❌ Component '${component}' missing`)
      allTestsPassed = false
    }
  })
  
  console.log(allTestsPassed ? '✅ Component structure validation passed\n' : '❌ Component structure validation failed\n')
  return allTestsPassed
}

// Test 4: Validate Configuration Files
function testConfiguration() {
  console.log('⚙️ Test 4: Configuration Validation')
  
  const configFiles = [
    'package.json',
    'tsconfig.json',
    'tailwind.config.js',
    'next.config.ts',
    '.env.example'
  ]
  
  let allTestsPassed = true
  
  configFiles.forEach(file => {
    const filePath = path.join(process.cwd(), file)
    if (fs.existsSync(filePath)) {
      console.log(`✅ Configuration file '${file}' found`)
    } else {
      console.log(`❌ Configuration file '${file}' missing`)
      allTestsPassed = false
    }
  })
  
  console.log(allTestsPassed ? '✅ Configuration validation passed\n' : '❌ Configuration validation failed\n')
  return allTestsPassed
}

// Run all tests
async function runTests() {
  const results = [
    testDatabaseSchema(),
    testAPIStructure(),
    testComponentStructure(),
    testConfiguration()
  ]
  
  const passedTests = results.filter(result => result).length
  const totalTests = results.length
  
  console.log('📊 Test Results Summary')
  console.log('======================')
  console.log(`✅ Passed: ${passedTests}/${totalTests} tests`)
  
  if (passedTests === totalTests) {
    console.log('🎉 All tests passed! Your debt management system is ready.')
    console.log('\n📋 Next Steps:')
    console.log('1. Run the database schema in Supabase SQL Editor')
    console.log('2. Configure your environment variables')
    console.log('3. Start the development server: npm run dev')
    console.log('4. Test payment functionality in the UI')
  } else {
    console.log('❌ Some tests failed. Please review the issues above.')
  }
}

// Run the tests
runTests().catch(console.error)
