# 🗄️ Database Setup - Quick Guide

## 🎯 One-Click Database Setup

Your Tindahan store database can be set up in **one simple step** using the consolidated schema file.

### ⚡ Quick Setup (Recommended)

1. **Open Supabase Dashboard**
   - Go to your Supabase project
   - Navigate to **SQL Editor**

2. **Copy & Paste Schema**
   - Open `database/schema.sql` in your project
   - **Select All** (Ctrl+A / Cmd+A)
   - **Copy** (Ctrl+C / Cmd+C)
   - **Paste** into Supabase SQL Editor (Ctrl+V / Cmd+V)

3. **Execute**
   - Click **"Run"** button
   - Wait for completion (should take 10-30 seconds)

4. **Verify Setup**
   - Check the output for success messages
   - You should see confirmation of tables, views, and sample data created

## ✅ What Gets Created

### 📊 Tables (4)
- **`products`** - Product inventory management
- **`customers`** - Customer profiles with Cloudinary support  
- **`customer_debts`** - Individual debt records
- **`customer_payments`** - Payment transaction history

### 🔍 Views (1)
- **`customer_balances`** - Real-time balance calculations with debt status

### ⚙️ Functions (2)
- **`update_updated_at_column()`** - Automatic timestamp management
- **`validate_payment_amount()`** - Payment validation logic

### 📈 Performance Features
- **14 Strategic Indexes** - Optimized for fast queries
- **5 Database Triggers** - Automatic timestamp updates
- **Business Logic Enforcement** - Data validation at database level

### 🎯 Sample Data
- **10 Products** - Ready-to-use product catalog
- **7 Customers** - Sample customer profiles
- **11 Debt Records** - Example debt transactions
- **5 Payment Records** - Sample payment history

## 🔧 Advanced Setup (Optional)

If you prefer to run individual migration files:

### Step 1: Main Schema
```sql
-- Run database/schema.sql first
```

### Step 2: Customer Profiles (if needed separately)
```sql  
-- Run database/migration_customer_profiles.sql
```

### Step 3: Cloudinary Support (if needed separately)
```sql
-- Run database/add_cloudinary_support.sql
```

## 🧪 Verification Queries

After setup, run these queries to verify everything works:

### Check Tables
```sql
SELECT table_name FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN ('products', 'customers', 'customer_debts', 'customer_payments');
```

### Check Views
```sql
SELECT table_name FROM information_schema.views 
WHERE table_schema = 'public' 
AND table_name = 'customer_balances';
```

### Test Balance Calculation
```sql
SELECT 
    customer_name || ' ' || customer_family_name as customer,
    '₱' || total_debt::text as total_debt,
    '₱' || total_payments::text as total_paid,
    '₱' || remaining_balance::text as balance,
    debt_status
FROM customer_balances 
ORDER BY remaining_balance DESC;
```

### Check Sample Data
```sql
SELECT 'Products: ' || COUNT(*) as count FROM products
UNION ALL
SELECT 'Customers: ' || COUNT(*) as count FROM customers  
UNION ALL
SELECT 'Debts: ' || COUNT(*) as count FROM customer_debts
UNION ALL
SELECT 'Payments: ' || COUNT(*) as count FROM customer_payments;
```

## 🚨 Troubleshooting

### Common Issues

**"Extension uuid-ossp does not exist"**
- Enable the UUID extension in Supabase Dashboard > Database > Extensions

**"Permission denied"**
- Ensure you're using the correct Supabase service role key
- Check your project permissions

**"Table already exists"**
- The schema uses `IF NOT EXISTS` - this is safe to re-run
- Drop existing tables if you want a fresh start

**"Sample data conflicts"**
- Clear existing data before re-running if needed:
```sql
TRUNCATE customer_payments, customer_debts, customers, products CASCADE;
```

## 🎯 Next Steps

After successful database setup:

1. **Configure Environment Variables**
   ```bash
   NEXT_PUBLIC_SUPABASE_URL=your_project_url
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key
   ```

2. **Start Development Server**
   ```bash
   npm run dev
   ```

3. **Test the System**
   ```bash
   node scripts/test-debt-system.js
   ```

4. **Access Admin Panel**
   - Navigate to `/admin`
   - Go to "Customer Debts" section
   - Test payment processing

## 📋 Database Schema Summary

```
┌─────────────────┐    ┌─────────────────┐
│    products     │    │    customers    │
├─────────────────┤    ├─────────────────┤
│ id (UUID)       │    │ id (UUID)       │
│ name            │    │ customer_name   │
│ price           │    │ customer_family │
│ stock_quantity  │    │ profile_pic_url │
│ category        │    │ phone_number    │
└─────────────────┘    └─────────────────┘
         │                       │
         │                       │
         ▼                       ▼
┌─────────────────┐    ┌─────────────────┐
│ customer_debts  │    │customer_payments│
├─────────────────┤    ├─────────────────┤
│ id (UUID)       │    │ id (UUID)       │
│ customer_name   │    │ customer_name   │
│ product_name    │    │ payment_amount  │
│ total_amount    │    │ payment_method  │
│ debt_date       │    │ payment_date    │
└─────────────────┘    └─────────────────┘
         │                       │
         └───────┬───────────────┘
                 ▼
      ┌─────────────────┐
      │customer_balances│ (VIEW)
      ├─────────────────┤
      │ customer_name   │
      │ total_debt      │
      │ total_payments  │
      │ remaining_bal   │
      │ debt_status     │
      └─────────────────┘
```

## ✨ Features Ready After Setup

- ✅ **Real-time Balance Calculation**
- ✅ **Automatic Payment Processing**  
- ✅ **Customer Debt Tracking**
- ✅ **Payment History Management**
- ✅ **Professional UI Components**
- ✅ **Performance Optimized Queries**
- ✅ **Sample Data for Testing**

Your debt management system is now ready for production use! 🎉
