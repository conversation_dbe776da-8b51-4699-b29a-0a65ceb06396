import { NextRequest, NextResponse } from 'next/server'

import {
  successResponse,
  with<PERSON><PERSON>r<PERSON><PERSON><PERSON>,
  handleDatabaseError,
  parsePaginationParams,
  handleCorsPreflightRequest
} from '@/lib/api-utils'
import { supabase } from '@/lib/supabase'

// Handle CORS preflight requests
export async function OPTIONS() {
  return handleCorsPreflightRequest()
}

// GET - Fetch all customer payments with pagination
export const GET = withErrorHandler(async (request: NextRequest) => {
  const { searchParams } = new URL(request.url)
  const { page, limit, offset } = parsePaginationParams(searchParams)

  // Optional filters
  const search = searchParams.get('search')
  const dateFrom = searchParams.get('dateFrom')
  const dateTo = searchParams.get('dateTo')
  const customer = searchParams.get('customer')

  let query = supabase
    .from('customer_payments')
    .select('*', { count: 'exact' })
    .order('created_at', { ascending: false })
    .range(offset, offset + limit - 1)

  // Apply filters
  if (search) {
    query = query.or(`customer_name.ilike.%${search}%,customer_family_name.ilike.%${search}%,notes.ilike.%${search}%`)
  }

  if (dateFrom) {
    query = query.gte('payment_date', dateFrom)
  }

  if (dateTo) {
    query = query.lte('payment_date', dateTo)
  }

  if (customer) {
    const [firstName, lastName] = customer.split(' ')
    if (lastName) {
      query = query.eq('customer_name', firstName).eq('customer_family_name', lastName)
    } else {
      query = query.or(`customer_name.ilike.%${customer}%,customer_family_name.ilike.%${customer}%`)
    }
  }

  const { data: payments, error, count } = await query

  if (error) {
    return handleDatabaseError(error)
  }

  return successResponse({
    payments,
    pagination: {
      page,
      limit,
      total: count || 0,
      totalPages: Math.ceil((count || 0) / limit)
    }
  })
})

// POST - Create new customer payment with enhanced validation and balance checking
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      customer_name,
      customer_family_name,
      payment_amount,
      payment_date,
      payment_method,
      notes,
    } = body

    // Validate required fields
    if (
      !customer_name ||
      !customer_family_name ||
      !payment_amount ||
      payment_amount <= 0
    ) {
      return NextResponse.json(
        { error: 'Missing required fields or invalid payment amount' },
        { status: 400 }
      )
    }

    // Get current customer balance before processing payment
    const { data: currentBalance, error: balanceError } = await supabase
      .from('customer_balances')
      .select('*')
      .eq('customer_name', customer_name)
      .eq('customer_family_name', customer_family_name)
      .single()

    if (balanceError && balanceError.code !== 'PGRST116') {
      console.error('Error fetching customer balance:', balanceError)
      return NextResponse.json(
        { error: 'Failed to verify customer balance' },
        { status: 500 }
      )
    }

    const remainingBalance = currentBalance?.remaining_balance || 0
    const paymentAmount = parseFloat(payment_amount)

    // Create the payment record
    const { data: payment, error } = await supabase
      .from('customer_payments')
      .insert([
        {
          customer_name,
          customer_family_name,
          payment_amount: paymentAmount,
          payment_date: payment_date || new Date().toISOString().split('T')[0],
          payment_method: payment_method || 'Cash',
          notes: notes || null,
        },
      ])
      .select()
      .single()

    if (error) {
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    // Get updated balance after payment
    const { data: updatedBalance, error: updatedBalanceError } = await supabase
      .from('customer_balances')
      .select('*')
      .eq('customer_name', customer_name)
      .eq('customer_family_name', customer_family_name)
      .single()

    if (updatedBalanceError && updatedBalanceError.code !== 'PGRST116') {
      console.warn('Could not fetch updated balance, but payment was successful')
    }

    // Prepare response with payment details and balance information
    const response = {
      payment,
      balance_info: {
        previous_balance: remainingBalance,
        payment_amount: paymentAmount,
        new_balance: updatedBalance?.remaining_balance || 0,
        overpayment: paymentAmount > remainingBalance && remainingBalance > 0,
        fully_paid: (updatedBalance?.remaining_balance || 0) <= 0
      }
    }

    return NextResponse.json(response, { status: 201 })
  } catch (error) {
    console.error('Error creating payment:', error)
    return NextResponse.json(
      { error: 'Failed to create payment record' },
      { status: 500 }
    )
  }
}
